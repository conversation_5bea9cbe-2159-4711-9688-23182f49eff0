# Adaptive Log Structure Learning System

## Overview

Transform Alfred from a domain-specific log analysis tool to a truly general log analysis agent by implementing dynamic log structure discovery. The system will learn log schemas automatically by analyzing sample data, eliminating the need for manual schema definitions.

## Core Concept

Instead of hardcoded log structure knowledge (like the current `log_structure` in `src/prompt_manager.py`), the system will:

1. **Auto-discover** log schemas by sampling recent logs
2. **Generate** appropriate query prompts based on discovered structure
3. **Adapt** to schema changes over time
4. **Generalize** across any application domain

## Implementation Architecture

### 1. Log Structure Discovery Service

**Location**: `src/structure_discovery/`

```
src/structure_discovery/
├── __init__.py
├── schema_analyzer.py      # Core analysis logic
├── field_extractor.py      # Field type & pattern detection
├── prompt_generator.py     # Generate prompts from schema
└── structure_cache.py      # Cache discovered structures
```

**Key Components**:

- **Schema Analyzer**: Samples N logs over configurable time period, identifies field patterns, data types, nested structures
- **Field Extractor**: Detects common identifiers, timestamps, status fields, nested objects
- **Prompt Generator**: Creates domain-agnostic query guidance based on discovered schema
- **Structure Cache**: Persists learned schemas with versioning

### 2. Bootstrap Configuration

**Environment Variables**:
```bash
# Sampling configuration
LOG_STRUCTURE_SAMPLE_SIZE=50          # Number of logs to analyze
LOG_STRUCTURE_SAMPLE_DAYS=30          # Days back to sample from
LOG_STRUCTURE_REFRESH_HOURS=24        # How often to refresh schema

# Discovery settings
ENABLE_AUTO_STRUCTURE_DISCOVERY=true
STRUCTURE_DISCOVERY_MODE=startup      # startup|background|manual
```

**Required Config** (only bootstrap knowledge):
- Database connection details
- Collection name
- Time field name (for sampling)

### 3. Startup Integration

**Modified Startup Flow**:

1. **Check Cache**: Look for existing structure definition
2. **Sample Recent Logs**: Query last N days for schema discovery
3. **Analyze Structure**: Extract field patterns, types, relationships
4. **Generate Prompts**: Create query guidance similar to current `log_structure`
5. **Cache Results**: Store for future use
6. **Initialize Agent**: Use generated prompts instead of hardcoded ones

### 4. Schema Analysis Logic

**Field Pattern Detection**:
- **Identifiers**: Fields matching UUID, sequential ID patterns
- **Timestamps**: Epoch, ISO 8601, custom formats
- **Status Fields**: Enum-like values (SUCCESS, FAILED, etc.)
- **Nested Objects**: Analyze depth and common nested patterns
- **Arrays**: Detect array fields and their element types

**Relationship Discovery**:
- Fields that appear to link records (foreign keys)
- Parent-child relationships in nested data
- Common grouping fields

**Query Pattern Generation**:
- Most frequent field combinations for filtering
- Common aggregation targets
- Time-based query patterns
- Error/success status patterns

### 5. Dynamic Prompt Generation

**Template System**:
```python
class DynamicPromptGenerator:
    def generate_log_structure_prompt(self, schema):
        """Generate log structure prompt from discovered schema"""
        # Create field documentation
        # Generate query examples
        # Build filtering guidance
        # Add optimization strategies
```

**Generated Sections**:
- Field documentation with types and examples
- Common query patterns for discovered identifiers
- Filtering strategies based on detected enums/statuses
- Timestamp handling for discovered time fields
- Optimization rules based on field cardinality

### 6. Verification & Updates

**Extended Goal - Automatic Refresh**:
- Periodic schema validation against recent logs
- Detect schema drift or new fields
- Trigger re-analysis when significant changes detected
- Version control for schema evolution

**Manual Override**:
- Admin interface to view/edit discovered schemas
- Force refresh capabilities
- Manual field mapping for complex cases

## Benefits

### Generalization
- **Zero Domain Knowledge**: Works with any log structure
- **Application Agnostic**: Adapts to microservices, monoliths, different frameworks
- **Schema Evolution**: Automatically adapts to application changes

### Operational Efficiency
- **Reduced Setup Time**: Eliminates manual schema documentation
- **Self-Healing**: Adapts to schema changes without intervention
- **Consistent Analysis**: Maintains query optimization across domains

### Scalability
- **Multi-Tenant**: Different applications with different schemas
- **Environment Agnostic**: Dev/staging/prod with schema variations
- **Framework Independent**: Works with any logging format

## Migration Strategy

### Phase 1: Core Implementation
1. Build schema discovery service
2. Implement dynamic prompt generation
3. Add caching layer
4. Test with current Alfred logs

### Phase 2: Integration
1. Modify startup flow to use discovery
2. Fallback to manual configuration if discovery fails
3. Add environment configuration
4. Performance testing and optimization

### Phase 3: Enhancement
1. Implement periodic refresh
2. Add schema versioning
3. Build admin interface for overrides
4. Multi-application support

## Success Metrics

- **Setup Time**: Reduce from hours to minutes for new applications
- **Accuracy**: Maintain current query accuracy with discovered schemas
- **Adaptability**: Successfully handle schema changes without manual intervention
- **Performance**: Schema discovery completes within reasonable time bounds

This system transforms Alfred into a truly general log analysis platform that can adapt to any application domain through intelligent structure discovery.
