# Adaptive Log Structure Learning Design

This document outlines the design for making the log analysis agent truly general by enabling it to automatically learn log structures from any application or domain.

## Current State

The system currently relies on a hardcoded log structure definition in `src/prompt_manager.py` that is specific to a particular application domain (loan applications, orders, etc.). This limits the agent's ability to work with logs from different applications or domains.

## Vision

Create a self-bootstrapping system that can:
1. Analyze sample logs from any MongoDB collection
2. Automatically infer the log structure and key patterns
3. Generate domain-specific prompts and query strategies
4. Continuously validate and refine its understanding

## Architecture Overview

### Core Components

1. **Log Structure Analyzer** - Analyzes sample logs to understand structure
2. **Prompt Generator** - Creates domain-specific prompts based on learned structure
3. **Structure Validator** - Periodically validates and updates understanding
4. **Configuration Manager** - Manages learned structures and validation schedules

### Bootstrap Process

The system will bootstrap with minimal configuration:
- MongoDB database name
- Collection name(s) to analyze
- Sample size and time window (configurable via environment)

## Implementation Plan

### Phase 1: Log Structure Analysis

#### 1.1 Sample Collection Service
```python
class LogSampleCollector:
    def collect_samples(self, days_back: int = 30, sample_size: int = 100) -> List[Dict]
    def get_representative_samples(self, logs: List[Dict]) -> List[Dict]
```

**Responsibilities:**
- Fetch N log samples from the last configurable period (default: 1 month)
- Ensure diverse sampling across time periods and log types
- Handle large collections efficiently with proper indexing

#### 1.2 Structure Analysis Engine
```python
class LogStructureAnalyzer:
    def analyze_structure(self, samples: List[Dict]) -> LogStructure
    def identify_key_fields(self, samples: List[Dict]) -> Dict[str, FieldInfo]
    def detect_patterns(self, samples: List[Dict]) -> List[Pattern]
```

**Analysis Tasks:**
- **Field Discovery**: Identify all fields, their types, and nesting levels
- **Key Identifier Detection**: Find fields that serve as unique identifiers (IDs, timestamps, etc.)
- **Pattern Recognition**: Detect common log patterns (request/response, error logs, business events)
- **Relationship Mapping**: Understand how different log entries relate to each other
- **Value Analysis**: Analyze field value distributions and common patterns

#### 1.3 Domain Context Inference
```python
class DomainContextInferrer:
    def infer_business_domain(self, samples: List[Dict]) -> BusinessDomain
    def identify_key_entities(self, samples: List[Dict]) -> List[Entity]
    def map_business_flows(self, samples: List[Dict]) -> List[BusinessFlow]
```

**Inference Tasks:**
- Identify business domain (e-commerce, fintech, healthcare, etc.)
- Extract key business entities (users, orders, applications, etc.)
- Map typical business flows and processes
- Understand error patterns and their business impact

### Phase 2: Prompt Generation

#### 2.1 Dynamic Prompt Builder
```python
class AdaptivePromptBuilder:
    def generate_log_structure_prompt(self, structure: LogStructure) -> str
    def create_persona_instructions(self, domain: BusinessDomain) -> Dict[str, str]
    def build_query_examples(self, structure: LogStructure) -> List[QueryExample]
```

**Generation Tasks:**
- Create log structure documentation similar to current `self.log_structure`
- Generate domain-specific persona instructions
- Build relevant query examples and patterns
- Create field-specific guidance for complex nested structures

#### 2.2 Prompt Template System
```python
class PromptTemplateManager:
    def load_base_templates(self) -> Dict[str, str]
    def customize_for_domain(self, templates: Dict, domain: BusinessDomain) -> Dict[str, str]
    def validate_generated_prompts(self, prompts: Dict) -> ValidationResult
```

### Phase 3: Integration & Startup

#### 3.1 Startup Integration
Modify the existing system to:
1. Check for existing learned structure on startup
2. If none exists, trigger the learning process
3. Load learned prompts into `PromptManager`
4. Set up validation schedules

#### 3.2 PromptManager Enhancement
```python
class EnhancedPromptManager:
    def __init__(self, config: EnvConfig):
        self.config = config
        self.learned_structure = self.load_or_learn_structure()
    
    def load_or_learn_structure(self) -> LearnedStructure:
        # Check for existing learned structure
        # If not found, trigger learning process
        # Return learned structure for prompt generation
```

### Phase 4: Continuous Learning (Extended Goal)

#### 4.1 Structure Validation Service
```python
class StructureValidator:
    def validate_current_understanding(self) -> ValidationResult
    def detect_schema_drift(self, new_samples: List[Dict]) -> DriftAnalysis
    def trigger_relearning(self, drift_analysis: DriftAnalysis) -> bool
```

**Validation Triggers:**
- Scheduled validation (weekly/monthly)
- Query failure rate thresholds
- New field detection
- Significant changes in log patterns

#### 4.2 Incremental Learning
- Update structure understanding without full relearning
- Merge new patterns with existing knowledge
- Maintain backward compatibility during transitions

## Configuration

### Environment Variables
```bash
# Learning Configuration
LOG_LEARNING_ENABLED=true
LOG_SAMPLE_DAYS_BACK=30
LOG_SAMPLE_SIZE=100
LOG_VALIDATION_SCHEDULE="0 2 * * 0"  # Weekly on Sunday at 2 AM

# Structure Storage
LEARNED_STRUCTURE_COLLECTION="learned_log_structures"
STRUCTURE_VALIDATION_COLLECTION="structure_validations"
```

### Learning Configuration Schema
```python
@dataclass
class LearningConfig:
    enabled: bool = True
    sample_days_back: int = 30
    sample_size: int = 100
    min_confidence_threshold: float = 0.8
    validation_schedule: str = "0 2 * * 0"
    force_relearn_threshold: float = 0.3  # Trigger relearn if confidence drops below this
```

## Data Models

### Learned Structure Storage
```python
@dataclass
class LearnedLogStructure:
    id: str
    database_name: str
    collection_name: str
    learned_at: datetime
    confidence_score: float
    field_schema: Dict[str, FieldInfo]
    key_identifiers: List[str]
    business_domain: str
    common_patterns: List[Pattern]
    generated_prompts: Dict[str, str]
    validation_history: List[ValidationResult]
```

### Field Information
```python
@dataclass
class FieldInfo:
    name: str
    type: str
    nested_level: int
    frequency: float  # How often this field appears
    sample_values: List[Any]
    is_identifier: bool
    business_meaning: Optional[str]
```

## Implementation Steps

### Step 1: Core Infrastructure (Week 1-2)
1. Create `LogSampleCollector` class
2. Implement basic structure analysis in `LogStructureAnalyzer`
3. Set up data models and storage schemas
4. Create configuration management

### Step 2: Analysis Engine (Week 3-4)
1. Implement comprehensive field discovery
2. Add pattern recognition capabilities
3. Build domain inference logic
4. Create confidence scoring system

### Step 3: Prompt Generation (Week 5-6)
1. Build `AdaptivePromptBuilder`
2. Create template system for different domains
3. Implement prompt validation
4. Test with various log types

### Step 4: Integration (Week 7-8)
1. Modify startup sequence to include learning
2. Enhance `PromptManager` for dynamic prompts
3. Add learning status endpoints
4. Implement fallback mechanisms

### Step 5: Validation System (Week 9-10)
1. Build structure validation service
2. Implement drift detection
3. Create relearning triggers
4. Add monitoring and alerting

## Success Metrics

1. **Accuracy**: Generated prompts produce relevant results for diverse log types
2. **Coverage**: System can handle 90%+ of common log structures automatically
3. **Confidence**: Learning process achieves >80% confidence scores
4. **Adaptability**: System detects and adapts to schema changes within 1 week
5. **Performance**: Learning process completes within 5 minutes for typical datasets

## Risk Mitigation

1. **Fallback Strategy**: Always maintain ability to use manual structure definitions
2. **Gradual Rollout**: Test with known log types before deploying to new domains
3. **Human Validation**: Provide interface for reviewing and correcting learned structures
4. **Version Control**: Maintain history of learned structures for rollback capability
5. **Monitoring**: Track query success rates and user satisfaction metrics

## Future Enhancements

1. **Multi-Collection Learning**: Analyze relationships across multiple collections
2. **Real-time Adaptation**: Update understanding based on live query patterns
3. **Cross-Domain Transfer**: Apply learnings from one domain to similar domains
4. **User Feedback Integration**: Incorporate user corrections to improve learning
5. **Advanced Pattern Recognition**: Use ML models for more sophisticated pattern detection

## Detailed Technical Specifications

### Log Sample Collection Strategy

**Sampling Algorithm:**
- **Time-based stratification**: Divide the time window into equal segments and sample from each
- **Log type diversity**: Ensure representation of different log types (errors, info, debug)
- **Size-based sampling**: For large collections, use reservoir sampling to maintain randomness
- **Deduplication**: Remove duplicate log entries based on content hash

**Query Optimization:**
```python
# Efficient sampling query with proper indexing
pipeline = [
    {"$match": {"timestamp": {"$gte": start_date, "$lte": end_date}}},
    {"$sample": {"size": sample_size}},
    {"$project": {"_id": 1, "timestamp": 1, "level": 1, "message": 1, "context": 1}}
]
```

### Structure Analysis Algorithms

**Field Discovery Process:**
1. **Recursive traversal** of all document structures
2. **Type inference** based on value patterns and MongoDB BSON types
3. **Frequency analysis** to identify optional vs required fields
4. **Nesting depth analysis** to understand document complexity

**Pattern Recognition:**
- **Request/Response patterns**: Identify paired log entries
- **Error correlation**: Link error logs to their triggering events
- **Business flow detection**: Map sequences of related log entries
- **Anomaly detection**: Identify unusual log patterns that may indicate issues

### AI-Powered Analysis

**LLM Integration for Structure Understanding:**
```python
class AIStructureAnalyzer:
    def analyze_with_llm(self, samples: List[Dict]) -> StructureInsights:
        prompt = f"""
        Analyze these log samples and identify:
        1. Business domain and key entities
        2. Important identifier fields
        3. Common log patterns and flows
        4. Error types and their business impact

        Samples: {json.dumps(samples[:10], indent=2)}
        """
        return self.llm_client.analyze(prompt)
```

**Confidence Scoring:**
- **Field consistency**: How consistently fields appear across samples
- **Pattern clarity**: How well-defined the identified patterns are
- **Domain recognition**: Confidence in business domain identification
- **Completeness**: Coverage of different log types and scenarios

### Integration Points

**Startup Sequence Modification:**
```python
class LogIntelligenceAgentLangGraph:
    def __init__(self, config: EnvConfig = None, env_file: str = None):
        # ... existing initialization ...

        # Initialize adaptive learning system
        self.learning_system = AdaptiveLearningSystem(self.config, self.database_manager)

        # Load or learn log structure
        self.learned_structure = self.learning_system.get_current_structure()

        # Initialize prompt manager with learned structure
        self.prompt_manager = PromptManager(learned_structure=self.learned_structure)
```

**PromptManager Integration:**
```python
class PromptManager:
    def __init__(self, learned_structure: LearnedLogStructure = None):
        if learned_structure:
            self.log_structure = learned_structure.generated_prompts["log_structure"]
            self.persona_instructions = learned_structure.generated_prompts["persona_instructions"]
        else:
            # Fallback to hardcoded prompts
            self.log_structure = self._get_default_log_structure()
            self.persona_instructions = self._get_default_persona_instructions()
```

### Monitoring and Observability

**Learning Process Metrics:**
- Learning duration and resource usage
- Confidence scores for different aspects
- Coverage metrics (fields discovered, patterns identified)
- Validation success rates

**Runtime Performance Metrics:**
- Query success rates with learned structure
- User satisfaction scores
- Error rates and types
- Response quality metrics

**Alerting Triggers:**
- Learning process failures
- Confidence score drops below threshold
- Significant schema drift detected
- Query failure rate increases

### Security and Privacy Considerations

**Data Privacy:**
- **Sample anonymization**: Remove or hash sensitive data in samples
- **Minimal data retention**: Store only necessary structure information
- **Access controls**: Restrict access to learning system components

**Security:**
- **Input validation**: Sanitize all log samples before analysis
- **Resource limits**: Prevent resource exhaustion during learning
- **Audit logging**: Track all learning activities and changes

### Testing Strategy

**Unit Testing:**
- Test individual components (collector, analyzer, generator)
- Mock MongoDB collections for consistent testing
- Validate data models and serialization

**Integration Testing:**
- Test end-to-end learning process
- Validate prompt generation quality
- Test startup integration

**Performance Testing:**
- Benchmark learning process with large datasets
- Test memory usage and processing time
- Validate query performance with learned structures

**Acceptance Testing:**
- Test with real log data from different domains
- Validate user experience with generated prompts
- Measure improvement in query accuracy and relevance

This comprehensive design provides a roadmap for building a truly general log analysis system that can adapt to any application domain while maintaining high performance and reliability.
