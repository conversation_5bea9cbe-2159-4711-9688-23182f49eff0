"""
Unified Provider Service for AI model abstraction.

This service provides a centralized interface for managing different AI providers
(Google GenAI, OpenAI, Anthropic) using LangChain/LangGraph abstraction.
"""

import logging
import threading
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional

from google.cloud import secretmanager
from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_core.callbacks.usage import UsageMetadataCallbackHandler
from langchain_core.language_models import BaseLanguageModel
from langchain_core.messages import BaseMessage
from langchain_google_vertexai import ChatVertexAI
from langchain_openai import ChatOpenAI

from src.database_manager import DatabaseManager
from src.env_config import EnvConfig


class ModelType(Enum):
    """Enumeration for different model usage types."""

    PRIMARY = "primary"
    LITE = "lite"
    SUMMARIZATION = "summarization"
    EMBEDDING = "embedding"


@dataclass
class ModelInfo:
    """Model information for registry."""

    model_id: str
    provider: str
    cost_per_input_token: float  # USD per 1M tokens
    cost_per_output_token: float  # USD per 1M tokens
    context_window: int
    supports_tools: bool = True
    supports_structured_output: bool = True


class UnifiedProviderService:
    """
    Centralized service for managing AI providers and models.

    Features:
    - Thread-safe model caching
    - Provider-agnostic cost calculation
    - LangChain model instance management
    - Unified logging interface
    """

    # Comprehensive model registry with costs and capabilities
    MODEL_REGISTRY: Dict[str, ModelInfo] = {
        # Google GenAI Models
        "gemini-2.5-pro": ModelInfo(
            model_id="gemini-2.5-pro",
            provider="google_genai",
            cost_per_input_token=1.25,
            cost_per_output_token=5.00,
            context_window=2097152,
            supports_tools=True,
            supports_structured_output=True,
        ),
        "gemini-2.5-flash": ModelInfo(
            model_id="gemini-2.5-flash",
            provider="google_genai",
            cost_per_input_token=0.075,
            cost_per_output_token=0.30,
            context_window=1048576,
            supports_tools=True,
            supports_structured_output=True,
        ),
        "text-embedding-004": ModelInfo(
            model_id="text-embedding-004",
            provider="google_genai",
            cost_per_input_token=0.00001,
            cost_per_output_token=0.0,
            context_window=2048,
            supports_tools=False,
            supports_structured_output=False,
        ),
        # OpenAI Models
        "gpt-4": ModelInfo(
            model_id="gpt-4",
            provider="openai",
            cost_per_input_token=30.0,
            cost_per_output_token=60.0,
            context_window=128000,
            supports_tools=True,
            supports_structured_output=True,
        ),
        "gpt-4o": ModelInfo(
            model_id="gpt-4o",
            provider="openai",
            cost_per_input_token=2.5,
            cost_per_output_token=10.0,
            context_window=128000,
            supports_tools=True,
            supports_structured_output=True,
        ),
        "gpt-4o-mini": ModelInfo(
            model_id="gpt-4o-mini",
            provider="openai",
            cost_per_input_token=0.15,
            cost_per_output_token=0.60,
            context_window=128000,
            supports_tools=True,
            supports_structured_output=True,
        ),
        "gpt-4.1": ModelInfo(
            model_id="gpt-4.1",
            provider="openai",
            cost_per_input_token=2.0,  # $2 per million tokens
            cost_per_output_token=8.0,  # $8 per million tokens
            context_window=1000000,  # 1M token context window
            supports_tools=True,
            supports_structured_output=True,
        ),
        "o3": ModelInfo(
            model_id="o3",
            provider="openai",
            cost_per_input_token=2.0,  # $2 per million tokens (current pricing after 80% reduction)
            cost_per_output_token=8.0,  # $8 per million tokens (current pricing after 80% reduction)
            context_window=200000,  # 200k token context window
            supports_tools=True,
            supports_structured_output=True,
        ),
        "text-embedding-3-large": ModelInfo(
            model_id="text-embedding-3-large",
            provider="openai",
            cost_per_input_token=0.13,
            cost_per_output_token=0.0,
            context_window=8192,
            supports_tools=False,
            supports_structured_output=False,
        ),
        # Anthropic Models
        "claude-opus-4-20250514": ModelInfo(
            model_id="claude-opus-4-20250514",
            provider="anthropic",
            cost_per_input_token=15.0,  # $15 per million tokens
            cost_per_output_token=75.0,  # $75 per million tokens
            context_window=200000,  # 200k token context window
            supports_tools=True,
            supports_structured_output=True,
        ),
        "claude-3-5-sonnet-20241022": ModelInfo(
            model_id="claude-3-5-sonnet-20241022",
            provider="anthropic",
            cost_per_input_token=3.0,
            cost_per_output_token=15.0,
            context_window=200000,
            supports_tools=True,
            supports_structured_output=True,
        ),
        "claude-3-5-haiku-20241022": ModelInfo(
            model_id="claude-3-5-haiku-20241022",
            provider="anthropic",
            cost_per_input_token=0.25,
            cost_per_output_token=1.25,
            context_window=200000,
            supports_tools=True,
            supports_structured_output=True,
        ),
    }

    def __init__(
        self, config: EnvConfig = None, database_manager: DatabaseManager = None
    ):
        """Initialize the UnifiedProviderService."""
        self.config = config or EnvConfig.load()
        self.database_manager = database_manager
        self.logger = logging.getLogger(__name__)

        # Thread-safe model cache
        self._model_cache: Dict[str, BaseLanguageModel] = {}
        self._cache_lock = threading.RLock()

        # Current model assignments based on config
        self._current_models = {
            ModelType.PRIMARY: self.config.primary_model,
            ModelType.LITE: self.config.lite_model,
            ModelType.SUMMARIZATION: self.config.summarization_model,
            ModelType.EMBEDDING: self.config.embedding_model,
        }

        # Initialize secret manager client for API keys
        self._secret_client = secretmanager.SecretManagerServiceClient()

        self.logger.info(
            "UnifiedProviderService initialized with models: %s", self._current_models
        )

    def get_langchain_model(self, model_type: ModelType) -> BaseLanguageModel:
        """
        Get a LangChain model instance for the specified type.
        Thread-safe with caching for performance.
        """
        model_id = self._current_models.get(model_type)
        if not model_id:
            raise ValueError(f"No model configured for type: {model_type}")

        cache_key = f"{model_type.value}_{model_id}"

        # Thread-safe cache check
        with self._cache_lock:
            if cache_key in self._model_cache:
                return self._model_cache[cache_key]

            # Create new model instance
            model_info = self.MODEL_REGISTRY.get(model_id)
            if not model_info:
                raise ValueError(f"Unknown model: {model_id}")

            langchain_model = self._create_langchain_model(model_info)

            # Cache the instance
            self._model_cache[cache_key] = langchain_model

            self.logger.info(
                f"Created and cached LangChain model: {model_id} for {model_type.value}"
            )
            return langchain_model

    def _access_secret(self, secret_id: str) -> str:
        """Access a secret from Google Secret Manager."""
        try:
            project_id = self.config.project_id
            name = f"projects/{project_id}/secrets/{secret_id}/versions/latest"
            response = self._secret_client.access_secret_version(request={"name": name})
            return response.payload.data.decode("UTF-8")
        except Exception as e:
            self.logger.error(f"Failed to access secret {secret_id}: {e}")
            raise

    def _create_langchain_model(self, model_info: ModelInfo) -> BaseLanguageModel:
        """Create a LangChain model instance based on provider."""
        if model_info.provider == "google_genai":
            return ChatVertexAI(
                model_name=model_info.model_id,
                project=self.config.project_id,
                location=self.config.gcp_location,
            )
        elif model_info.provider == "openai":
            # Get OpenAI API key from Secret Manager
            openai_api_key = self._access_secret("OPENAI_API_KEY")
            return ChatOpenAI(
                model=model_info.model_id,
                api_key=openai_api_key,
            )
        elif model_info.provider == "anthropic":
            # Get Anthropic API key from Secret Manager
            anthropic_api_key = self._access_secret("ANTHROPIC_API_KEY")
            return ChatAnthropic(
                model=model_info.model_id,
                api_key=anthropic_api_key,
            )
        else:
            raise ValueError(f"Unsupported provider: {model_info.provider}")

    def calculate_cost(
        self, model_id: str, input_tokens: int, output_tokens: int
    ) -> float:
        """Calculate cost for a model call using token counts."""
        model_info = self.MODEL_REGISTRY.get(model_id)
        if not model_info:
            self.logger.warning(f"Unknown model for cost calculation: {model_id}")
            return 0.0

        input_cost = (input_tokens / 1_000_000) * model_info.cost_per_input_token
        output_cost = (output_tokens / 1_000_000) * model_info.cost_per_output_token

        total_cost = input_cost + output_cost

        self.logger.debug(
            f"Cost calculation - Model: {model_id}, "
            f"Input: {input_tokens} tokens (${input_cost:.4f}), "
            f"Output: {output_tokens} tokens (${output_cost:.4f}), "
            f"Total: ${total_cost:.4f}"
        )

        return total_cost

    def call_langchain_model(
        self,
        model_type: ModelType,
        messages: List[BaseMessage],
        session_id: str,
        run_id: str,
        persona: str = None,
        step_description: str = None,
        tools: List = None,
        structured_output_schema: Any = None,
    ) -> Any:
        """
        Call a LangChain model with unified error handling, retries, and logging.

        Args:
            model_type: Type of model to use (PRIMARY, LITE, etc.)
            messages: List of LangChain messages
            session_id: Session identifier
            run_id: Run identifier
            persona: Optional persona
            step_description: Description of the step for logging
            tools: Optional list of tools to bind to the model
            structured_output_schema: Optional Pydantic model for structured output

        Returns:
            LangChain response object or structured output object
        """
        # Get the appropriate model
        model = self.get_langchain_model(model_type)

        # Bind tools if provided
        if tools:
            model = model.bind_tools(tools)

        # Add structured output if provided
        if structured_output_schema:
            model = model.with_structured_output(structured_output_schema)

        # Add retry capability
        model_with_retry = model.with_retry(
            retry_if_exception_type=(Exception,),
            stop_after_attempt=3,
            wait_exponential_jitter=True,
        )

        # Make the call with exception handling and usage tracking
        try:
            # Use UsageMetadataCallbackHandler to track token usage
            from langchain_core.callbacks.usage import UsageMetadataCallbackHandler

            usage_callback = UsageMetadataCallbackHandler()

            response = model_with_retry.invoke(
                messages, config={"callbacks": [usage_callback]}
            )

            # Log the response using the unified service
            usage_metadata = self.log_langchain_response(
                response=response,
                model_type=model_type,
                session_id=session_id,
                run_id=run_id,
                persona=persona,
                step_description=step_description,
                usage_callback=usage_callback,
                input_messages=messages,
            )

            # Always return a tuple of (response, usage_metadata) for consistency
            return response, usage_metadata

        except Exception as e:
            logging.exception(
                f"🤖 Model call failed for {step_description}",
                extra={
                    "details": {
                        "error_message": str(e),
                        "error_type": type(e).__name__,
                        "model_type": model_type.value,
                        "session_id": session_id,
                        "run_id": run_id,
                    }
                },
            )
            raise

    def log_langchain_response(
        self,
        response: Any,
        model_type: ModelType,
        session_id: str,
        run_id: str,
        persona: str = None,
        step_description: str = None,
        usage_callback: UsageMetadataCallbackHandler = None,
        input_messages: List = None,
    ) -> Dict[str, Any]:
        """
        Provider-agnostic logging for LangChain responses.

        Args:
            response: LangChain response object or structured output
            model_type: Type of model used
            session_id: Session identifier
            run_id: Run identifier
            persona: Optional persona used
            step_description: Optional description of the step
            usage_callback: UsageMetadataCallbackHandler for token usage tracking
            input_messages: List of input messages sent to the model

        Returns:
            Dict containing extracted metadata for further processing
        """
        model_id = self._current_models.get(model_type, "unknown")

        # Extract usage metadata from callback (works for both regular and structured outputs)
        if usage_callback and usage_callback.usage_metadata:
            # UsageMetadataCallbackHandler stores usage per model name
            model_usage = usage_callback.usage_metadata.get(model_id, {})
            input_tokens = model_usage.get("input_tokens", 0)
            output_tokens = model_usage.get("output_tokens", 0)
            total_tokens = model_usage.get("total_tokens", input_tokens + output_tokens)
        else:
            # Fallback to response metadata (for regular responses)
            usage_metadata = getattr(response, "usage_metadata", {})
            input_tokens = usage_metadata.get("input_tokens", 0)
            output_tokens = usage_metadata.get("output_tokens", 0)
            total_tokens = input_tokens + output_tokens

        # Calculate cost
        cost = self.calculate_cost(model_id, input_tokens, output_tokens)

        # Extract content and tool calls using LangChain's standard interface
        content = getattr(response, "content", "")
        tool_calls = getattr(response, "tool_calls", [])

        # Prepare log metadata
        log_metadata = {
            "model_id": model_id,
            "model_type": model_type.value,
            "provider": self.MODEL_REGISTRY.get(
                model_id, ModelInfo("", "", 0, 0, 0)
            ).provider,
            "session_id": session_id,
            "run_id": run_id,
            "persona": persona,
            "step_description": step_description,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "total_tokens": total_tokens,
            "cost_usd": cost,
            "content_length": len(content),
            "tool_calls_count": len(tool_calls),
            "has_tool_calls": len(tool_calls) > 0,
            "input_messages": [
                {
                    "type": type(msg).__name__,
                    "content": getattr(msg, "content", str(msg))[:1000]
                    + ("..." if len(getattr(msg, "content", str(msg))) > 1000 else ""),
                    "tool_calls": getattr(msg, "tool_calls", None),
                    "name": getattr(msg, "name", None),
                    "tool_call_id": getattr(msg, "tool_call_id", None),
                }
                for msg in (input_messages or [])
            ],
            "response_type": type(response).__name__,
            "response_content": str(response)[:1000]
            + ("..." if len(str(response)) > 1000 else ""),
            "response_tool_calls": getattr(response, "tool_calls", None),
        }

        # Log summary
        self.logger.info(
            f"LangChain response logged - {step_description or 'Unknown Step'} "
            f"Tokens: {input_tokens}+{output_tokens}={total_tokens}, "
            f"Cost: ${cost:.4f}",
            extra={"details": log_metadata},
        )

        # Store in database if available
        if self.database_manager:
            try:
                self.database_manager.log_llm_interaction(
                    session_id=session_id,
                    run_id=run_id,
                    model_name=model_id,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                    cost=cost,
                    persona=persona,
                    step_type=step_description,
                )
            except Exception as e:
                self.logger.error(f"Failed to log to database: {e}")

        return log_metadata

    def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """Get model information from registry."""
        return self.MODEL_REGISTRY.get(model_id)

    def list_available_models(self, provider: str = None) -> List[ModelInfo]:
        """List all available models, optionally filtered by provider."""
        models = list(self.MODEL_REGISTRY.values())

        if provider:
            models = [m for m in models if m.provider == provider]

        return models

    # Note: Dynamic model switching is not supported in this implementation
    # Models are configured at startup and remain constant for the session

    def cleanup(self):
        """
        Clean up UnifiedProviderService resources.
        Called by client_managers.py during shutdown.
        """
        try:
            # Clear model cache
            with self._cache_lock:
                self._model_cache.clear()

            self.logger.info("UnifiedProviderService cleaned up successfully")

        except Exception as e:
            self.logger.error(f"Error during UnifiedProviderService cleanup: {e}")

    def is_available(self) -> bool:
        """Check if the service is available and properly configured."""
        try:
            # Verify at least one model is configured
            return bool(self._current_models.get(ModelType.PRIMARY))
        except Exception:
            return False
